# Task3.1.2 Javascript Test Maintenance Template

Following the progression of the codebase, the `calculateDiscountedPrice` function from our previous practical task has evolved into the `calculateDiscountedPriceWithTax` function. This enhancement introduces tax rates into the calculation. Your challenge now is to perform test maintenance on the unit tests you crafted earlier. You must update them to capture this new functionality while ensuring the tests remain comprehensive and robust.

The function to test is in the template.

It is recommended that you complete this task with the help of [EPAM Dial](https://chat.lab.epam.com/).