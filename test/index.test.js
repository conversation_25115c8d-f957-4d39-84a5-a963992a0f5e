const { expect } = require("chai");
const calculateDiscountedPriceWithTax = require("../src/index");

describe("calculateDiscountedPriceWithTax", function () {
    describe("Input Validation", function () {
        it("should throw an error when originalPrice is not a number", function () {
            expect(() =>
                calculateDiscountedPriceWithTax("100", 10, 5)
            ).to.throw("Invalid input type");
        });

        it("should throw an error when discountPercentage is not a number", function () {
            expect(() =>
                calculateDiscountedPriceWithTax(100, "10", 5)
            ).to.throw("Invalid input type");
        });

        it("should throw an error when taxRate is not a number", function () {
            expect(() =>
                calculateDiscountedPriceWithTax(100, 10, "5")
            ).to.throw("Invalid input type");
        });

        it("should throw an error when originalPrice is null", function () {
            expect(() => calculateDiscountedPriceWithTax(null, 10, 5)).to.throw(
                "Invalid input type"
            );
        });

        it("should throw an error when originalPrice is undefined", function () {
            expect(() =>
                calculateDiscountedPriceWithTax(undefined, 10, 5)
            ).to.throw("Invalid input type");
        });

        it("should throw an error when discountPercentage is less than 0", function () {
            expect(() => calculateDiscountedPriceWithTax(100, -1, 5)).to.throw(
                "Discount percentage should be between 0 and 100"
            );
        });

        it("should throw an error when discountPercentage is greater than 100", function () {
            expect(() => calculateDiscountedPriceWithTax(100, 101, 5)).to.throw(
                "Discount percentage should be between 0 and 100"
            );
        });

        it("should throw an error when taxRate is less than 0", function () {
            expect(() => calculateDiscountedPriceWithTax(100, 10, -1)).to.throw(
                "Tax rate should be between 0 and 100"
            );
        });

        it("should throw an error when taxRate is greater than 100", function () {
            expect(() =>
                calculateDiscountedPriceWithTax(100, 10, 101)
            ).to.throw("Tax rate should be between 0 and 100");
        });
    });

    describe("Basic Calculations", function () {
        it("should calculate correctly with no discount and no tax", function () {
            const result = calculateDiscountedPriceWithTax(100, 0, 0);
            expect(result).to.equal(100);
        });

        it("should calculate correctly with discount but no tax", function () {
            const result = calculateDiscountedPriceWithTax(100, 20, 0);
            expect(result).to.equal(80);
        });

        it("should calculate correctly with no discount but with tax", function () {
            const result = calculateDiscountedPriceWithTax(100, 0, 10);
            expect(result).to.equal(110);
        });

        it("should calculate correctly with both discount and tax", function () {
            const result = calculateDiscountedPriceWithTax(100, 20, 10);
            expect(result).to.equal(88); // 100 - 20 = 80, then 80 + 8 = 88
        });

        it("should use default tax rate of 0 when not provided", function () {
            const result = calculateDiscountedPriceWithTax(100, 20);
            expect(result).to.equal(80);
        });
    });

    describe("Edge Cases", function () {
        it("should handle zero original price", function () {
            const result = calculateDiscountedPriceWithTax(0, 20, 10);
            expect(result).to.equal(0);
        });

        it("should handle 100% discount", function () {
            const result = calculateDiscountedPriceWithTax(100, 100, 10);
            expect(result).to.equal(0);
        });

        it("should handle 100% tax rate", function () {
            const result = calculateDiscountedPriceWithTax(100, 0, 100);
            expect(result).to.equal(200);
        });

        it("should handle 100% discount and 100% tax", function () {
            const result = calculateDiscountedPriceWithTax(100, 100, 100);
            expect(result).to.equal(0);
        });

        it("should handle decimal original price", function () {
            const result = calculateDiscountedPriceWithTax(99.99, 10, 5);
            expect(result).to.be.closeTo(94.4895, 0.0001);
        });

        it("should handle decimal discount percentage", function () {
            const result = calculateDiscountedPriceWithTax(100, 12.5, 8);
            expect(result).to.equal(94.5); // 100 - 12.5 = 87.5, then 87.5 + 7 = 94.5
        });

        it("should handle decimal tax rate", function () {
            const result = calculateDiscountedPriceWithTax(100, 10, 7.5);
            expect(result).to.equal(96.75); // 100 - 10 = 90, then 90 + 6.75 = 96.75
        });
    });

    describe("Real-world Scenarios", function () {
        it("should calculate correctly for a typical e-commerce scenario", function () {
            // $50 item with 15% discount and 8.25% sales tax
            const result = calculateDiscountedPriceWithTax(50, 15, 8.25);
            expect(result).to.be.closeTo(46.01, 0.01);
        });

        it("should calculate correctly for a high-value item", function () {
            // $1000 item with 25% discount and 12% tax
            const result = calculateDiscountedPriceWithTax(1000, 25, 12);
            expect(result).to.equal(840); // 1000 - 250 = 750, then 750 + 90 = 840
        });

        it("should calculate correctly for a small purchase", function () {
            // $5.99 item with 5% discount and 6% tax
            const result = calculateDiscountedPriceWithTax(5.99, 5, 6);
            expect(result).to.be.closeTo(6.03, 0.01);
        });

        it("should handle large discount with small tax", function () {
            // $200 item with 90% discount and 2% tax
            const result = calculateDiscountedPriceWithTax(200, 90, 2);
            expect(result).to.equal(20.4); // 200 - 180 = 20, then 20 + 0.4 = 20.4
        });

        it("should handle small discount with large tax", function () {
            // $100 item with 5% discount and 25% tax
            const result = calculateDiscountedPriceWithTax(100, 5, 25);
            expect(result).to.equal(118.75); // 100 - 5 = 95, then 95 + 23.75 = 118.75
        });
    });

    describe("Boundary Values", function () {
        it("should handle minimum valid discount percentage (0)", function () {
            const result = calculateDiscountedPriceWithTax(100, 0, 10);
            expect(result).to.equal(110);
        });

        it("should handle maximum valid discount percentage (100)", function () {
            const result = calculateDiscountedPriceWithTax(100, 100, 10);
            expect(result).to.equal(0);
        });

        it("should handle minimum valid tax rate (0)", function () {
            const result = calculateDiscountedPriceWithTax(100, 20, 0);
            expect(result).to.equal(80);
        });

        it("should handle maximum valid tax rate (100)", function () {
            const result = calculateDiscountedPriceWithTax(100, 20, 100);
            expect(result).to.equal(160); // 100 - 20 = 80, then 80 + 80 = 160
        });
    });

    describe("Precision and Rounding", function () {
        it("should maintain precision for complex calculations", function () {
            const result = calculateDiscountedPriceWithTax(123.45, 33.33, 7.77);
            const expected = 123.45 * (1 - 0.3333) * (1 + 0.0777);
            expect(result).to.be.closeTo(expected, 0.0001);
        });

        it("should handle calculations that result in many decimal places", function () {
            const result = calculateDiscountedPriceWithTax(99.99, 33.33, 16.67);
            // 99.99 - 33.33 = 66.66, then 66.66 + 11.11 = 77.77
            expect(result).to.be.closeTo(77.77, 0.01);
        });
    });
});
