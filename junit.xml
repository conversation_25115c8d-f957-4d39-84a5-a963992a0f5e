<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Mocha Tests" time="0.0040" tests="32" failures="0">
  <testsuite name="Root Suite" timestamp="2025-06-17T13:57:01" tests="0" time="0.0000" failures="0">
  </testsuite>
  <testsuite name="calculateDiscountedPriceWithTax" timestamp="2025-06-17T13:57:01" tests="0" file="/home/<USER>/learn-to-code/task3.1.2-javascript-test-maintenance-copilot/test/index.test.js" time="0.0000" failures="0">
  </testsuite>
  <testsuite name="Input Validation" timestamp="2025-06-17T13:57:01" tests="9" file="/home/<USER>/learn-to-code/task3.1.2-javascript-test-maintenance-copilot/test/index.test.js" time="0.0020" failures="0">
    <testcase name="calculateDiscountedPriceWithTax Input Validation should throw an error when originalPrice is not a number" time="0.0010" classname="should throw an error when originalPrice is not a number">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Input Validation should throw an error when discountPercentage is not a number" time="0.0000" classname="should throw an error when discountPercentage is not a number">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Input Validation should throw an error when taxRate is not a number" time="0.0000" classname="should throw an error when taxRate is not a number">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Input Validation should throw an error when originalPrice is null" time="0.0000" classname="should throw an error when originalPrice is null">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Input Validation should throw an error when originalPrice is undefined" time="0.0000" classname="should throw an error when originalPrice is undefined">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Input Validation should throw an error when discountPercentage is less than 0" time="0.0010" classname="should throw an error when discountPercentage is less than 0">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Input Validation should throw an error when discountPercentage is greater than 100" time="0.0000" classname="should throw an error when discountPercentage is greater than 100">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Input Validation should throw an error when taxRate is less than 0" time="0.0000" classname="should throw an error when taxRate is less than 0">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Input Validation should throw an error when taxRate is greater than 100" time="0.0000" classname="should throw an error when taxRate is greater than 100">
    </testcase>
  </testsuite>
  <testsuite name="Basic Calculations" timestamp="2025-06-17T13:57:01" tests="5" file="/home/<USER>/learn-to-code/task3.1.2-javascript-test-maintenance-copilot/test/index.test.js" time="0.0010" failures="0">
    <testcase name="calculateDiscountedPriceWithTax Basic Calculations should calculate correctly with no discount and no tax" time="0.0010" classname="should calculate correctly with no discount and no tax">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Basic Calculations should calculate correctly with discount but no tax" time="0.0000" classname="should calculate correctly with discount but no tax">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Basic Calculations should calculate correctly with no discount but with tax" time="0.0000" classname="should calculate correctly with no discount but with tax">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Basic Calculations should calculate correctly with both discount and tax" time="0.0000" classname="should calculate correctly with both discount and tax">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Basic Calculations should use default tax rate of 0 when not provided" time="0.0000" classname="should use default tax rate of 0 when not provided">
    </testcase>
  </testsuite>
  <testsuite name="Edge Cases" timestamp="2025-06-17T13:57:01" tests="7" file="/home/<USER>/learn-to-code/task3.1.2-javascript-test-maintenance-copilot/test/index.test.js" time="0.0000" failures="0">
    <testcase name="calculateDiscountedPriceWithTax Edge Cases should handle zero original price" time="0.0000" classname="should handle zero original price">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Edge Cases should handle 100% discount" time="0.0000" classname="should handle 100% discount">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Edge Cases should handle 100% tax rate" time="0.0000" classname="should handle 100% tax rate">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Edge Cases should handle 100% discount and 100% tax" time="0.0000" classname="should handle 100% discount and 100% tax">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Edge Cases should handle decimal original price" time="0.0000" classname="should handle decimal original price">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Edge Cases should handle decimal discount percentage" time="0.0000" classname="should handle decimal discount percentage">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Edge Cases should handle decimal tax rate" time="0.0000" classname="should handle decimal tax rate">
    </testcase>
  </testsuite>
  <testsuite name="Real-world Scenarios" timestamp="2025-06-17T13:57:01" tests="5" file="/home/<USER>/learn-to-code/task3.1.2-javascript-test-maintenance-copilot/test/index.test.js" time="0.0010" failures="0">
    <testcase name="calculateDiscountedPriceWithTax Real-world Scenarios should calculate correctly for a typical e-commerce scenario" time="0.0000" classname="should calculate correctly for a typical e-commerce scenario">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Real-world Scenarios should calculate correctly for a high-value item" time="0.0000" classname="should calculate correctly for a high-value item">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Real-world Scenarios should calculate correctly for a small purchase" time="0.0010" classname="should calculate correctly for a small purchase">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Real-world Scenarios should handle large discount with small tax" time="0.0000" classname="should handle large discount with small tax">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Real-world Scenarios should handle small discount with large tax" time="0.0000" classname="should handle small discount with large tax">
    </testcase>
  </testsuite>
  <testsuite name="Boundary Values" timestamp="2025-06-17T13:57:01" tests="4" file="/home/<USER>/learn-to-code/task3.1.2-javascript-test-maintenance-copilot/test/index.test.js" time="0.0000" failures="0">
    <testcase name="calculateDiscountedPriceWithTax Boundary Values should handle minimum valid discount percentage (0)" time="0.0000" classname="should handle minimum valid discount percentage (0)">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Boundary Values should handle maximum valid discount percentage (100)" time="0.0000" classname="should handle maximum valid discount percentage (100)">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Boundary Values should handle minimum valid tax rate (0)" time="0.0000" classname="should handle minimum valid tax rate (0)">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Boundary Values should handle maximum valid tax rate (100)" time="0.0000" classname="should handle maximum valid tax rate (100)">
    </testcase>
  </testsuite>
  <testsuite name="Precision and Rounding" timestamp="2025-06-17T13:57:01" tests="2" file="/home/<USER>/learn-to-code/task3.1.2-javascript-test-maintenance-copilot/test/index.test.js" time="0.0000" failures="0">
    <testcase name="calculateDiscountedPriceWithTax Precision and Rounding should maintain precision for complex calculations" time="0.0000" classname="should maintain precision for complex calculations">
    </testcase>
    <testcase name="calculateDiscountedPriceWithTax Precision and Rounding should handle calculations that result in many decimal places" time="0.0000" classname="should handle calculations that result in many decimal places">
    </testcase>
  </testsuite>
</testsuites>