/**
 * Calculate the total cost after applying a discount and adding tax.
 *
 * @param {number} originalPrice - The original price of the item.
 * @param {number} discountPercentage - The discount percentage (0 to 100).
 * @param {number} taxRate - The tax rate percentage (0 to 100). Default is 0 if not provided.
 * @returns {number} - The total cost after discount and including tax.
 */



function calculateDiscountedPriceWithTax(originalPrice, discountPercentage, taxRate = 0) {
    if (typeof originalPrice !== "number" || typeof discountPercentage !== "number" || typeof taxRate !== "number") {
        throw new Error("Invalid input type");
    }

    if (discountPercentage < 0 || discountPercentage > 100) {
        throw new Error("Discount percentage should be between 0 and 100");
    }

    if (taxRate < 0 || taxRate > 100) {
        throw new Error("Tax rate should be between 0 and 100");
    }

    const discount = originalPrice * (discountPercentage / 100);
    const afterDiscountPrice = originalPrice - discount;
    const tax = afterDiscountPrice * (taxRate / 100);

    return afterDiscountPrice + tax;
}

module.exports  = calculateDiscountedPriceWithTax
